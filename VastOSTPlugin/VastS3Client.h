/*
 *************************************************************************
 * Vast Data S3 Client
 * Handles S3 API communication for data operations
 *************************************************************************
 */

#ifndef _VAST_S3_CLIENT_H_
#define _VAST_S3_CLIENT_H_

#include <string>
#include <vector>
#include <map>
#include <memory>

// S3 Object metadata
struct VastS3ObjectInfo {
    std::string key;
    std::string etag;
    uint64_t size;
    std::string last_modified;
    std::string storage_class;
    std::map<std::string, std::string> metadata;
};

// S3 Bucket information
struct VastS3BucketInfo {
    std::string name;
    std::string creation_date;
    std::string region;
    std::map<std::string, std::string> tags;
};

// S3 Multipart upload info
struct VastS3MultipartUpload {
    std::string upload_id;
    std::string key;
    std::vector<std::string> part_etags;
    int next_part_number;
};

class VastS3Client {
public:
    VastS3Client();
    ~VastS3Client();

    // Initialization
    int initialize(const std::string& s3_endpoint, 
                   const std::string& access_key, 
                   const std::string& secret_key,
                   const std::string& region = "us-east-1",
                   bool use_https = true);

    // Bucket operations
    int createBucket(const std::string& bucket_name);
    int deleteBucket(const std::string& bucket_name);
    int listBuckets(std::vector<std::string>& bucket_names);
    int listBuckets(std::vector<VastS3BucketInfo>& buckets);  // Alternative signature with full bucket info
    int bucketExists(const std::string& bucket_name, bool& exists);
    int headBucket(const std::string& bucket_name);

    // Object operations
    int putObject(const std::string& bucket_name, 
                  const std::string& key, 
                  const void* data, 
                  uint64_t size,
                  const std::map<std::string, std::string>& metadata = {});

    int getObject(const std::string& bucket_name, 
                  const std::string& key, 
                  void* buffer, 
                  uint64_t buffer_size, 
                  uint64_t offset = 0,
                  uint64_t* bytes_read = nullptr);

    int getObjectInfo(const std::string& bucket_name, 
                      const std::string& key, 
                      VastS3ObjectInfo& object_info);

    int deleteObject(const std::string& bucket_name, const std::string& key);

    int listObjects(const std::string& bucket_name,
                    const std::string& prefix,
                    std::vector<VastS3ObjectInfo>& objects,
                    int max_keys = 1000);

    int listObjects(const std::string& bucket_name, const std::string& prefix,
                   std::vector<VastS3ObjectInfo>& objects, const std::string& continuation_token = "");

    int putObject(const std::string& bucket_name, const std::string& object_key,
                 const std::string& file_path, const std::map<std::string, std::string>& metadata = {});

    int getObject(const std::string& bucket_name, const std::string& object_key,
                 const std::string& file_path, uint64_t offset = 0, uint64_t length = 0);

    int headObject(const std::string& bucket_name, const std::string& object_key,
                  VastS3ObjectInfo& object_info);

    // Range operations for large objects
    int getObjectRange(const std::string& bucket_name, 
                       const std::string& key, 
                       void* buffer, 
                       uint64_t offset, 
                       uint64_t length,
                       uint64_t* bytes_read = nullptr);

    int putObjectRange(const std::string& bucket_name, 
                       const std::string& key, 
                       const void* data, 
                       uint64_t offset, 
                       uint64_t length);

    // Multipart upload operations
    int initiateMultipartUpload(const std::string& bucket_name,
                                const std::string& key,
                                VastS3MultipartUpload& upload);

    int initiateMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                               std::string& upload_id);

    int uploadPart(const VastS3MultipartUpload& upload,
                   int part_number,
                   const void* data,
                   uint64_t size,
                   std::string& etag);

    int uploadPart(const std::string& bucket_name, const std::string& object_key,
                  const std::string& upload_id, int part_number,
                  const std::string& file_path, uint64_t offset, uint64_t size,
                  std::string& etag);

    int completeMultipartUpload(VastS3MultipartUpload& upload);
    int completeMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                               const std::string& upload_id,
                               const std::vector<VastS3PartInfo>& parts);

    int abortMultipartUpload(const VastS3MultipartUpload& upload);
    int abortMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                            const std::string& upload_id);

    // Object metadata operations
    int setObjectMetadata(const std::string& bucket_name, 
                          const std::string& key, 
                          const std::map<std::string, std::string>& metadata);

    int getObjectMetadata(const std::string& bucket_name, 
                          const std::string& key, 
                          std::map<std::string, std::string>& metadata);

    // Copy operations
    int copyObject(const std::string& src_bucket,
                   const std::string& src_key,
                   const std::string& dst_bucket,
                   const std::string& dst_key);

    int copyObject(const std::string& source_bucket, const std::string& source_key,
                  const std::string& dest_bucket, const std::string& dest_key);

    // Server-side encryption support
    int setEncryption(const std::string& encryption_type, const std::string& kms_key = "");
    
    // Lifecycle management
    int setObjectLifecycle(const std::string& bucket_name, 
                           const std::string& policy_json);

    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

    // Configuration
    void setTimeout(int timeout_seconds);
    void setRetryCount(int retry_count);
    void setPartSize(uint64_t part_size); // For multipart uploads

    // Cleanup
    void cleanup();

private:
    std::string m_endpoint;
    std::string m_s3_endpoint;  // S3 endpoint URL
    std::string m_access_key;
    std::string m_secret_key;
    std::string m_region;
    bool m_use_https;
    bool m_verify_ssl;          // SSL verification flag
    int m_timeout_seconds;
    int m_retry_count;
    uint64_t m_part_size;

    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;

    // S3 client implementation (AWS SDK or custom implementation)
    void* m_s3_client;

    // Helper methods
    std::string generateSignature(const std::string& method, 
                                  const std::string& bucket, 
                                  const std::string& key,
                                  const std::map<std::string, std::string>& headers);
    
    std::string buildS3Url(const std::string& bucket_name, const std::string& object_key,
                           const std::map<std::string, std::string>& query_params) const;

    int makeS3Request(const std::string& method, const std::string& bucket_name,
                     const std::string& object_key, const std::string& payload,
                     const std::map<std::string, std::string>& query_params,
                     S3Response& response, const std::map<std::string, std::string>& extra_headers = {});

    int makeS3RequestWithFile(const std::string& method, const std::string& bucket_name,
                             const std::string& object_key, FILE* file, uint64_t file_size,
                             const std::map<std::string, std::string>& extra_headers,
                             S3Response& response, const std::map<std::string, std::string>& query_params = {});

    int makeS3RequestWithBody(const std::string& method, const std::string& bucket_name,
                             const std::string& object_key, const std::string& body,
                             const std::map<std::string, std::string>& extra_headers,
                             S3Response& response, const std::map<std::string, std::string>& query_params = {});

    int makeHttpRequest(const std::string& method, const std::string& url,
                       const std::string& body, const std::map<std::string, std::string>& headers,
                       S3Response& response);

    int makeHttpRequestWithFile(const std::string& method, const std::string& url,
                               FILE* file, uint64_t file_size, const std::map<std::string, std::string>& headers,
                               S3Response& response);

    std::string generateAwsSignature(const std::string& method, const std::string& bucket_name,
                                    const std::string& object_key, const std::map<std::string, std::string>& query_params,
                                    const std::map<std::string, std::string>& headers, const std::string& payload);

    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string urlEncode(const std::string& input);
    std::string base64Encode(const std::string& input);
};

#endif /* _VAST_S3_CLIENT_H_ */