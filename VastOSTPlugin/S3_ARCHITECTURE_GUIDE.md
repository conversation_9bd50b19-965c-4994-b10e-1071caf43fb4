# Vast Data S3 Object Storage Architecture for NetBackup OST

## Executive Summary

This document clarifies the relationship between Vast Data Views and S3 Buckets in the context of NetBackup OST plugin implementation, specifically for S3 object storage operations.

## Key Architectural Principle

**S3 Buckets = LSUs (Logical Storage Units)**
**Views = Configuration Context (NOT LSUs)**

## Detailed Architecture

### 1. **S3 Buckets as LSUs**

```
NetBackup OST Plugin View:
├── Storage Server: "vast-cluster-prod"
│   ├── LSU 1: S3 Bucket "netbackup-prod-daily"
│   ├── LSU 2: S3 Bucket "netbackup-prod-weekly" 
│   ├── LSU 3: S3 Bucket "netbackup-prod-monthly"
│   ├── LSU 4: S3 Bucket "netbackup-archive-longterm"
│   └── LSU 5: S3 Bucket "netbackup-dev-testing"
```

**Why S3 Buckets = LSUs:**
- S3 buckets are the primary storage containers for backup objects
- Each bucket provides isolated namespace for backup images
- NetBackup expects LSUs to be logical storage containers
- S3 API operations work directly on buckets
- Bucket-level policies, versioning, and lifecycle management

### 2. **Views as Configuration Context**

```
Vast Data Internal Organization:
├── Tenant: "production"
│   ├── View: "netbackup-prod" (s3_enabled=true)
│   │   ├── Policy: "high-performance-ssd"
│   │   ├── Quota: 100TB
│   │   └── S3 Buckets: netbackup-prod-*
│   └── View: "netbackup-archive" (s3_enabled=true)
│       ├── Policy: "long-term-retention"
│       ├── Quota: 500TB
│       └── S3 Buckets: netbackup-archive-*
├── Tenant: "development"
│   └── View: "netbackup-dev" (s3_enabled=true)
│       ├── Policy: "standard-performance"
│       ├── Quota: 10TB
│       └── S3 Buckets: netbackup-dev-*
```

**What Views Provide:**
- **Tenant Isolation**: Organize S3 buckets by department/environment
- **Storage Policies**: QoS, performance tiers, encryption settings
- **Quota Management**: Capacity limits for groups of S3 buckets
- **Access Control**: Who can create/access S3 buckets in this context
- **S3 Enablement**: `s3_enabled=true` indicates S3 API availability

### 3. **API Usage Pattern**

#### **Phase 1: Discovery and Context (VMS REST API)**
```cpp
// 1. Authenticate to VMS
POST /api/token/
{
  "username": "netbackup-service",
  "password": "secure-password"
}

// 2. Discover S3-enabled views for context
GET /api/views/
[
  {
    "name": "netbackup-prod",
    "tenant": "production", 
    "s3_enabled": true,
    "policy": "high-performance-ssd"
  },
  {
    "name": "netbackup-archive",
    "tenant": "production",
    "s3_enabled": true, 
    "policy": "long-term-retention"
  }
]

// 3. Create S3 credentials for each tenant context
POST /api/s3keys/
{
  "name": "netbackup-prod-key",
  "user": "netbackup-service",
  "tenant": "production"
}
```

#### **Phase 2: LSU Operations (AWS S3 SDK)**
```cpp
// 4. Use S3 credentials to enumerate LSUs (buckets)
AWS S3 SDK: ListBuckets()
[
  "netbackup-prod-daily",
  "netbackup-prod-weekly", 
  "netbackup-prod-monthly",
  "netbackup-archive-longterm",
  "netbackup-dev-testing"
]

// 5. Each bucket becomes an LSU in NetBackup
// 6. All data operations use S3 API on buckets
AWS S3 SDK: PutObject("netbackup-prod-daily", "backup-image-001.nbf")
AWS S3 SDK: GetObject("netbackup-prod-daily", "backup-image-001.nbf")
```

## Implementation Code Structure

### **VastStorageServer: LSU Enumeration**
```cpp
int VastStorageServer::enumerateLSUs() {
    // Step 1: Get S3-enabled views for context
    std::vector<VastViewInfo> views;
    m_rest_client->listViews(views);
    
    // Step 2: For each S3-enabled view, get tenant context
    for (const auto& view : views) {
        if (view.s3_enabled) {
            // Get S3 credentials for this view's tenant
            VastS3Key s3_key;
            m_rest_client->createS3Key("netbackup-" + view.name, view.tenant_name, s3_key);
            
            // Initialize S3 client with tenant credentials
            m_s3_client->initialize(m_s3_endpoint, s3_key.access_key, s3_key.secret_key);
            
            // List S3 buckets in this tenant context
            std::vector<VastS3BucketInfo> buckets;
            m_s3_client->listBuckets(buckets);
            
            // Each bucket becomes an LSU
            for (const auto& bucket : buckets) {
                VastLSU* lsu = new VastLSU(bucket.name, this);
                lsu->setViewContext(view);  // Store view context for policies
                lsu->setS3Credentials(s3_key);
                m_lsu_cache[bucket.name] = lsu;
            }
        }
    }
    return STS_SUCCESS;
}
```

### **VastLSU: S3 Bucket Operations**
```cpp
class VastLSU {
private:
    std::string m_bucket_name;      // S3 bucket name (LSU identifier)
    VastViewInfo m_view_context;    // Parent view for policies/quotas
    VastS3Key m_s3_credentials;     // S3 access credentials
    
public:
    // All LSU operations work on the S3 bucket
    int createImage(const std::string& image_name) {
        return m_s3_client->putObject(m_bucket_name, image_name, image_data);
    }
    
    int deleteImage(const std::string& image_name) {
        return m_s3_client->deleteObject(m_bucket_name, image_name);
    }
    
    int listImages(std::vector<std::string>& images) {
        return m_s3_client->listObjects(m_bucket_name, images);
    }
};
```

## Benefits of This Architecture

### **1. Proper Separation of Concerns**
- **Views**: Management, policies, tenant organization
- **S3 Buckets**: Data storage and NetBackup LSU operations

### **2. Scalability**
- Multiple S3 buckets per view for different backup types
- Tenant-based isolation and resource management
- Policy inheritance from view to buckets

### **3. NetBackup Integration**
- Clean LSU mapping (1 bucket = 1 LSU)
- Standard S3 API for all data operations
- Proper quota and capacity reporting

### **4. Vast Data Best Practices**
- Leverages tenant multi-tenancy features
- Uses storage policies for performance optimization
- Maintains proper access control and isolation

## Summary

- **Views are NOT LSUs** - they provide organizational context
- **S3 Buckets ARE LSUs** - they store the actual backup data
- **Both are needed** - Views for management, buckets for storage
- **API separation** - VMS REST API for control, S3 SDK for data
- **Tenant isolation** - Views organize buckets by tenant/department

This architecture ensures optimal integration between NetBackup OST and Vast Data's S3 object storage capabilities while maintaining proper multi-tenancy and policy management.
