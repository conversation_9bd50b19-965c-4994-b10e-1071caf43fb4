# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DUSE_MACOS_COMPAT_HEADERS=1 -Dvastost_EXPORTS

CXX_INCLUDES = -I/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin/../OST-SDK-11.1.1/src/include -I/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin -I/opt/homebrew/Cellar/jsoncpp/1.9.6/include -isystem /opt/homebrew/Cellar/curl/8.14.0/include -isystem /opt/homebrew/Cellar/openssl@3/3.5.0/include

CXX_FLAGSarm64 = -O3 -DNDEBUG -std=gnu++17 -arch arm64 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden

CXX_FLAGS = -O3 -DNDEBUG -std=gnu++17 -arch arm64 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden

