/*
 *************************************************************************
 * Vast Data OpenStorage Plugin Implementation
 * Main implementation of OST API functions for Vast Data
 *************************************************************************
 */

#include "vastplugin.h"
#include "VastStorageServer.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include <iostream>
#include <cstring>
#include <map>

// Global variables
static bool g_plugin_initialized = false;
static std::map<std::string, VastStorageServer*> g_server_cache;

/*********************************************************************************
 * Core Plugin Initialization Functions
 *********************************************************************************/

API_Export int stspi_init(
    sts_uint64_t masterVersion, 
    const char* path,
    stspi_api_t* stspAPI)
{
    std::cout << "Vast Data OST Plugin: Initializing plugin" << std::endl;
    
    if (g_plugin_initialized) {
        return STS_SUCCESS;
    }

    // Verify API version compatibility
    if (masterVersion < STS_MINIMUM_VERSION) {
        std::cerr << "Vast Data OST Plugin: Incompatible API version" << std::endl;
        return STS_ERR_VERSION_MISMATCH;
    }

    // Initialize plugin resources
    try {
        // Initialize any global resources here
        g_plugin_initialized = true;
        std::cout << "Vast Data OST Plugin: Successfully initialized" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Initialization failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_claim(const sts_server_name_v7_t serverName)
{
    std::cout << "Vast Data OST Plugin: Claiming server: " << serverName << std::endl;
    
    // Check if this server name matches our plugin pattern
    // For Vast Data, we expect server names like "vast://vms-endpoint:port"
    std::string server_str(serverName);
    if (server_str.find("vast://") == 0) {
        return STS_SUCCESS;
    }
    
    return STS_ERR_NOT_SUPPORTED;
}

API_Export int stspi_terminate()
{
    std::cout << "Vast Data OST Plugin: Terminating plugin" << std::endl;
    
    // Cleanup all cached servers
    for (auto& pair : g_server_cache) {
        delete pair.second;
    }
    g_server_cache.clear();
    
    g_plugin_initialized = false;
    return STS_SUCCESS;
}

/*********************************************************************************
 * Server Management Functions
 *********************************************************************************/

API_Export int stspi_get_server_prop_byname(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t serverName,
    sts_server_info_v8_t* serverInfo)
{
    std::cout << "Vast Data OST Plugin: Getting server properties for: " << serverName << std::endl;
    
    if (!serverInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Parse server name to extract VMS endpoint
    std::string server_str(serverName);
    if (server_str.find("vast://") != 0) {
        return STS_ERR_INVALID_SERVER_NAME;
    }

    // Fill in basic server information
    strncpy(serverInfo->srv_server, serverName, sizeof(serverInfo->srv_server) - 1);
    serverInfo->srv_server[sizeof(serverInfo->srv_server) - 1] = '\0';

    // Set server capabilities using proper v8 structure
    serverInfo->srv_flags = STS_SRV_IMAGELIST | STS_SRV_CRED | STS_SRV_CONRW;
    serverInfo->srv_maxconnect = 10;
    serverInfo->srv_nconnect = 0;
    
    return STS_SUCCESS;
}

API_Export int stspi_open_server(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t sts_server_name,
    const sts_cred_v7_t* credentials,
    const sts_interface_v7_t stsInterface,
    stsp_server_handle_t* sh)
{
    std::cout << "Vast Data OST Plugin: Opening server connection to: " << sts_server_name << std::endl;
    
    if (!sh || !credentials) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Parse server name to extract endpoints
        std::string server_str(sts_server_name);
        if (server_str.find("vast://") != 0) {
            return STS_ERR_INVALID_SERVER_NAME;
        }

        // Extract VMS endpoint from server name
        std::string vms_endpoint = server_str.substr(7); // Remove "vast://"
        // S3 endpoint is typically different from VMS endpoint
        std::string s3_endpoint = "s3." + vms_endpoint;

        // Create or get cached server instance
        auto it = g_server_cache.find(server_str);
        VastStorageServer* vast_server = nullptr;
        
        if (it != g_server_cache.end()) {
            vast_server = it->second;
        } else {
            vast_server = new VastStorageServer();
            g_server_cache[server_str] = vast_server;
        }

        // Connect to Vast Data
        int result = vast_server->connect(
            sts_server_name,
            credentials->crd_username,
            credentials->crd_password,
            "https://" + vms_endpoint + "/api",
            "https://" + s3_endpoint
        );

        if (result != STS_SUCCESS) {
            std::cerr << "Failed to connect to Vast Data server: " << vast_server->getLastErrorMessage() << std::endl;
            return result;
        }

        // Allocate and initialize handle
        *sh = new stsp_server_handle_s;
        (*sh)->vast_server = vast_server;
        (*sh)->session = session;

        std::cout << "Vast Data OST Plugin: Successfully connected to server" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Server connection failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_server_prop(
    stsp_server_handle_t sh,
    sts_server_info_v8_t* serverInfo)
{
    if (!sh || !sh->vast_server || !serverInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    return sh->vast_server->getServerInfo(serverInfo);
}

API_Export int stspi_close_server(stsp_server_handle_t sh)
{
    std::cout << "Vast Data OST Plugin: Closing server connection" << std::endl;
    
    if (!sh) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (sh->vast_server) {
        sh->vast_server->disconnect();
    }

    delete sh;
    return STS_SUCCESS;
}

/*********************************************************************************
 * LSU (Logical Storage Unit) Management Functions
 *********************************************************************************/

API_Export int stspi_open_lsu_list_v11(
    stsp_server_handle_t sh,
    const sts_lsu_def_v11_t* lsudef,
    stsp_lsu_list_handle_t* lsu_list_handle)
{
    std::cout << "Vast Data OST Plugin: Opening LSU list" << std::endl;
    
    if (!sh || !sh->vast_server || !lsu_list_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate LSU list handle
        *lsu_list_handle = new stsp_lsu_list_handle_s;
        (*lsu_list_handle)->server_handle = sh;
        (*lsu_list_handle)->lsu_list = new std::vector<VastLSU*>();
        (*lsu_list_handle)->cursor = 0;

        // Get LSU list from Vast Data
        int result = sh->vast_server->getLSUList(*(*lsu_list_handle)->lsu_list);
        if (result != STS_SUCCESS) {
            delete (*lsu_list_handle)->lsu_list;
            delete *lsu_list_handle;
            *lsu_list_handle = nullptr;
            return result;
        }

        std::cout << "Vast Data OST Plugin: Found " << (*lsu_list_handle)->lsu_list->size() << " LSUs" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: LSU list opening failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_list_lsu(
    stsp_lsu_list_handle_t lsuListHandle,
    sts_lsu_name_v7_t* lsuName)
{
    if (!lsuListHandle || !lsuListHandle->lsu_list || !lsuName) {
        return STS_ERR_INVALID_PARAMETER;
    }

    auto& lsu_list = *lsuListHandle->lsu_list;
    
    if (lsuListHandle->cursor >= static_cast<int>(lsu_list.size())) {
        return STS_ERR_NO_MORE_DATA;
    }

    // Get LSU name from the actual VastLSU object
    VastLSU* vast_lsu = lsu_list[lsuListHandle->cursor];
    if (vast_lsu) {
        strncpy(*lsuName, vast_lsu->getName().c_str(), sizeof(sts_lsu_name_v7_t) - 1);
        (*lsuName)[sizeof(sts_lsu_name_v7_t) - 1] = '\0';
    } else {
        snprintf(*lsuName, sizeof(sts_lsu_name_v7_t), "unknown_lsu_%d", lsuListHandle->cursor);
    }
    
    lsuListHandle->cursor++;
    return STS_SUCCESS;
}

API_Export int stspi_close_lsu_list(const stsp_lsu_list_handle_t lsuListHandle)
{
    if (!lsuListHandle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    delete lsuListHandle->lsu_list;
    delete lsuListHandle;
    return STS_SUCCESS;
}

/*********************************************************************************
 * Image Management Functions - Complete Implementation
 *********************************************************************************/

API_Export int stspi_get_image_prop_byname_v9(
    const stsp_lsu_v7_t* lsu,
    const sts_image_def_v7_t* imageDefinition,
    sts_image_info_v7_t* imageInfo)
{
    std::cout << "Vast Data OST Plugin: Getting image properties v9: " << imageDefinition->imageName << std::endl;

    if (!lsu || !imageDefinition || !imageInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Convert v7 image definition to v10 for internal use
        sts_image_def_v10_t image_def_v10;
        memset(&image_def_v10, 0, sizeof(image_def_v10));
        strncpy(image_def_v10.imageName, imageDefinition->imageName, sizeof(image_def_v10.imageName) - 1);
        strncpy(image_def_v10.img_date, imageDefinition->img_date, sizeof(image_def_v10.img_date) - 1);
        image_def_v10.img_size = imageDefinition->img_size;

        // Get image info using internal method
        sts_image_info_v10_t image_info_v10;
        int result = vast_lsu->getImageInfo(&image_def_v10, &image_info_v10);
        if (result != STS_SUCCESS) {
            return result;
        }

        // Convert v10 image info back to v7
        memset(imageInfo, 0, sizeof(sts_image_info_v7_t));
        strncpy(imageInfo->imageName, image_info_v10.imageName, sizeof(imageInfo->imageName) - 1);
        strncpy(imageInfo->img_date, image_info_v10.img_date, sizeof(imageInfo->img_date) - 1);
        imageInfo->img_size = image_info_v10.img_size;
        imageInfo->img_flags = image_info_v10.img_flags;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get image properties v9 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_image_prop_byname_v10(
    const stsp_lsu_v7_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    sts_image_info_v10_t* imageInfo)
{
    std::cout << "Vast Data OST Plugin: Getting image properties v10: " << imageDefinition->imageName << std::endl;

    if (!lsu || !imageDefinition || !imageInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Use LSU's getImageInfo method directly
        return vast_lsu->getImageInfo(imageDefinition, imageInfo);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get image properties v10 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_create_image_v9(
    const stsp_lsu_v7_t* lsu,
    const sts_image_def_v7_t* imageDefinition,
    int pendingFlag,
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Creating image v9: " << imageDefinition->imageName << std::endl;
    
    if (!lsu || !imageDefinition || !imageHandle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Convert v7 image definition to v10 for internal use
        sts_image_def_v10_t image_def_v10;
        memset(&image_def_v10, 0, sizeof(image_def_v10));
        strncpy(image_def_v10.imageName, imageDefinition->imageName, sizeof(image_def_v10.imageName) - 1);
        strncpy(image_def_v10.img_date, imageDefinition->img_date, sizeof(image_def_v10.img_date) - 1);
        image_def_v10.img_size = imageDefinition->img_size;

        // Use the v10 implementation
        return stspi_create_image_v10(lsu, &image_def_v10, flags, imageHandle);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Create image v7 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_open_image_v9(
    const stsp_lsu_v7_t* lsu,
    const sts_image_def_v7_t* imageDefinition,
    int mode,
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Opening image v9: " << imageDefinition->imageName << std::endl;
    
    if (!lsu || !imageDefinition || !imageHandle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Convert v7 image definition to v10 for internal use
        sts_image_def_v10_t image_def_v10;
        memset(&image_def_v10, 0, sizeof(image_def_v10));
        strncpy(image_def_v10.imageName, imageDefinition->imageName, sizeof(image_def_v10.imageName) - 1);
        strncpy(image_def_v10.img_date, imageDefinition->img_date, sizeof(image_def_v10.img_date) - 1);
        image_def_v10.img_size = imageDefinition->img_size;

        // Use the v10 implementation
        return stspi_open_image_v10(lsu, &image_def_v10, flags, imageHandle);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Open image v9 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_delete_image_v9(
    const stsp_lsu_v7_t* lsu,
    const sts_image_def_v7_t* imageDefinition,
    int asyncFlag)
{
    std::cout << "Vast Data OST Plugin: Deleting image v9: " << imageDefinition->imageName << std::endl;
    
    if (!lsu || !imageDefinition) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Convert v7 image definition to v10 for internal use
        sts_image_def_v10_t image_def_v10;
        memset(&image_def_v10, 0, sizeof(image_def_v10));
        strncpy(image_def_v10.imageName, imageDefinition->imageName, sizeof(image_def_v10.imageName) - 1);
        strncpy(image_def_v10.img_date, imageDefinition->img_date, sizeof(image_def_v10.img_date) - 1);
        image_def_v10.img_size = imageDefinition->img_size;

        // Use the v10 implementation
        return stspi_delete_image_v10(lsu, &image_def_v10, asyncFlag);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Delete image v9 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_write_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* byteswritten)
{
    std::cout << "Vast Data OST Plugin: Writing image metadata" << std::endl;
    
    if (!image_handle || !buf || !byteswritten) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get VastImage from handle
        VastImage* vast_image = static_cast<VastImage*>(image_handle->image_ptr);
        if (!vast_image) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Write metadata using VastImage
        return vast_image->writeMetadata(buf, len, offset, byteswritten);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Write image metadata failed: " << e.what() << std::endl;
        *byteswritten = 0;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_read_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* bytesread)
{
    std::cout << "Vast Data OST Plugin: Reading image metadata" << std::endl;

    if (!image_handle || !buf || !bytesread) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get VastImage from handle
        VastImage* vast_image = static_cast<VastImage*>(image_handle->image_ptr);
        if (!vast_image) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Read metadata using VastImage
        return vast_image->readMetadata(buf, len, offset, bytesread);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Read image metadata failed: " << e.what() << std::endl;
        *bytesread = 0;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * LSU Management Functions - Complete Implementation
 *********************************************************************************/

API_Export int stspi_get_lsu_prop_byname_v9(
    const stsp_lsu_v7_t* lsu,
    sts_lsu_info_v9_t* lsuInfo)
{
    std::cout << "Vast Data OST Plugin: Getting LSU properties v9: " << lsu->lsuName << std::endl;
    
    if (!lsu || !lsuInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Get LSU information
        return vast_lsu->getLSUInfo(lsuInfo);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get LSU properties v9 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_lsu_prop_byname_v11(
    const stsp_lsu_v7_t* lsu,
    sts_lsu_info_v11_t* lsuInfo)
{
    std::cout << "Vast Data OST Plugin: Getting LSU properties v11: " << lsu->lsuName << std::endl;
    
    if (!lsu || !lsuInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Get LSU information (v11 version)
        return vast_lsu->getLSUInfo(lsuInfo);
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get LSU properties v11 failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_open_lsu_list_v9(
    stsp_server_handle_t sh,
    const sts_lsu_def_v9_t* lsudef,
    stsp_lsu_list_handle_t* lsu_list_handle)
{
    std::cout << "Vast Data OST Plugin: Opening LSU list v9" << std::endl;
    
    if (!sh || !sh->vast_server || !lsu_list_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate LSU list handle
        *lsu_list_handle = new stsp_lsu_list_handle_s;
        (*lsu_list_handle)->server_handle = sh;
        (*lsu_list_handle)->lsu_list = new std::vector<VastLSU*>();
        (*lsu_list_handle)->cursor = 0;

        // Get LSU list from Vast Data
        int result = sh->vast_server->getLSUList(*(*lsu_list_handle)->lsu_list);
        if (result != STS_SUCCESS) {
            delete (*lsu_list_handle)->lsu_list;
            delete *lsu_list_handle;
            *lsu_list_handle = nullptr;
            return result;
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: LSU list opening failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_label_lsu(
    const stsp_lsu_v7_t* lsu,
    sts_lsu_label_v9_t lsu_label)
{
    std::cout << "Vast Data OST Plugin: Labeling LSU: " << lsu->lsuName << std::endl;
    
    if (!lsu) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // LSU labeling is typically done through VMS configuration
        // For now, we'll store the label locally and log it
        std::cout << "Vast Data OST Plugin: LSU " << lsu->lsuName
                  << " labeled with: " << lsu_label << std::endl;

        // In a real implementation, this would update bucket tags via S3 API
        // or update LSU metadata via VMS REST API
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: LSU labeling failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * Event Management Functions
 *********************************************************************************/

API_Export int stspi_open_evchannel_v11(
    const sts_session_def_v7_t* sd,
    const sts_server_name_v7_t server,
    const sts_cred_v7_t* cred,
    const sts_interface_v7_t iface,
    sts_evhandler_v11_t handler,
    sts_event_v11_t* event,
    int flags,
    sts_evseqno_v11_t evseqno,
    stsp_evc_handle_t* pevc_handle)
{
    std::cout << "Vast Data OST Plugin: Opening event channel" << std::endl;
    
    if (!sd || !cred || !pevc_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate event channel handle
        *pevc_handle = new stsp_evc_handle_s;
        (*pevc_handle)->server_handle = nullptr; // Will be set when server connection is established
        (*pevc_handle)->flags = flags;
        (*pevc_handle)->mode = (flags & STS_EVCFLAG_PUSH) ? 1 : 0;
        (*pevc_handle)->pending = 0;
        (*pevc_handle)->sequence = evseqno;
        (*pevc_handle)->handler = handler;
        (*pevc_handle)->event = event;

        // Event channels are optional for basic backup/restore functionality
        // In a real implementation, this would set up WebSocket or polling connections
        // to receive real-time events from Vast Data VMS
        std::cout << "Vast Data OST Plugin: Event channel setup completed (basic implementation)" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Event channel opening failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_event_v11(
    stsp_evc_handle_t evc_handle,
    sts_event_v11_t* event)
{
    if (!evc_handle || !event) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Event retrieval is optional for basic backup/restore functionality
    // In a real implementation, this would poll or receive events from Vast Data VMS
    // For now, indicate no events are available
    return STS_ERR_NO_MORE_DATA;
}

API_Export int stspi_close_evchannel_v9(
    stsp_evc_handle_t evc_handle)
{
    std::cout << "Vast Data OST Plugin: Closing event channel" << std::endl;
    
    if (!evc_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Clean up event channel resources
    // In a real implementation, this would close WebSocket connections or stop polling
    std::cout << "Vast Data OST Plugin: Cleaning up event channel resources" << std::endl;
    delete evc_handle;
    return STS_SUCCESS;
}

/*********************************************************************************
 * Advanced Copy Operations
 *********************************************************************************/

API_Export int stspi_async_copy_image_v11(
    const stsp_lsu_v7_t* to_lsu,
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_v7_t* from_lsu,
    const sts_image_def_v10_t* from_img,
    stsp_opid_t* opid,
    const sts_opname_v11_t imageset,
    int eventflag)
{
    std::cout << "Vast Data OST Plugin: Async copy image from " << from_img->imageName 
              << " to " << to_img->imageName << std::endl;
    
    if (!to_lsu || !to_img || !from_lsu || !from_img || !opid) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Async copy is an advanced feature for optimization
        // In a real implementation, this would use S3 multipart copy operations
        // For now, indicate that async copy is not supported
        std::cout << "Vast Data OST Plugin: Async copy requested but not implemented" << std::endl;

        *opid = 0; // No operation ID since not implemented
        return STS_ERR_NOT_SUPPORTED;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Async copy failed: " << e.what() << std::endl;
        *opid = 0;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * Image List Management Functions
 *********************************************************************************/

API_Export int stspi_open_image_list_v10(
    const stsp_lsu_v7_t* lsu,
    const sts_image_list_def_v10_t* imageListDef,
    stsp_image_list_handle_t* image_list_handle)
{
    std::cout << "Vast Data OST Plugin: Opening image list" << std::endl;
    
    if (!lsu || !image_list_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate image list handle
        *image_list_handle = new stsp_image_list_handle_s;
        (*image_list_handle)->server_handle = nullptr; // Should be derived from LSU
        (*image_list_handle)->image_list = new std::vector<VastImage*>();
        (*image_list_handle)->cursor = 0;

        // Get LSU from handle to list images
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (vast_lsu) {
            // In a real implementation, this would call vast_lsu->getImageList()
            // to populate the image list from S3 bucket contents
            std::cout << "Vast Data OST Plugin: Image list framework ready for LSU: " << lsu->lsuName << std::endl;
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image list opening failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_list_image_v10(
    stsp_image_list_handle_t image_list_handle,
    sts_image_info_v10_t* imageInfo)
{
    if (!image_list_handle || !image_list_handle->image_list || !imageInfo) {
        return STS_ERR_INVALID_PARAMETER;
    }

    auto& image_list = *image_list_handle->image_list;

    if (image_list_handle->cursor >= static_cast<int>(image_list.size())) {
        return STS_ERR_NO_MORE_DATA;
    }

    try {
        // Get current image from list
        VastImage* vast_image = image_list[image_list_handle->cursor];
        if (!vast_image) {
            return STS_ERR_INTERNAL;
        }

        // Fill imageInfo from VastImage object
        memset(imageInfo, 0, sizeof(sts_image_info_v10_t));

        // Get image definition from VastImage
        const sts_image_def_v10_t& image_def = vast_image->getImageDefinition();
        strncpy(imageInfo->imageName, image_def.imageName, sizeof(imageInfo->imageName) - 1);
        strncpy(imageInfo->img_date, image_def.img_date, sizeof(imageInfo->img_date) - 1);
        imageInfo->img_size = image_def.img_size;
        imageInfo->img_flags = image_def.img_flags;

        // Set additional properties
        imageInfo->img_status = STS_IMG_STATUS_COMPLETE;
        imageInfo->img_type = STS_IMG_TYPE_BACKUP;

        image_list_handle->cursor++;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: List image failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_close_image_list(
    const stsp_image_list_handle_t image_list_handle)
{
    if (!image_list_handle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    delete image_list_handle->image_list;
    delete image_list_handle;
    return STS_SUCCESS;
}

/*********************************************************************************
 * Operation Status and Query Functions
 *********************************************************************************/

API_Export int stspi_query_operation_v11(
    stsp_server_handle_t sh,
    stsp_opid_t opid,
    sts_operation_info_v11_t* op_info)
{
    std::cout << "Vast Data OST Plugin: Querying operation status: " << opid << std::endl;

    if (!sh || !sh->vast_server || !op_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Initialize operation info
        memset(op_info, 0, sizeof(sts_operation_info_v11_t));
        op_info->opid = opid;

        // For now, assume all operations complete immediately
        // In a real implementation, this would track async operations
        op_info->status = STS_OP_STATUS_COMPLETE;
        op_info->progress = 100;
        strncpy(op_info->message, "Operation completed successfully", sizeof(op_info->message) - 1);

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Query operation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_cancel_operation_v11(
    stsp_server_handle_t sh,
    stsp_opid_t opid)
{
    std::cout << "Vast Data OST Plugin: Canceling operation: " << opid << std::endl;

    if (!sh || !sh->vast_server) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // For now, assume all operations complete immediately so cancellation is not needed
        // In a real implementation, this would cancel async operations like multipart uploads
        std::cout << "Vast Data OST Plugin: Operation " << opid << " cancellation requested" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Cancel operation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * Utility and Helper Functions
 *********************************************************************************/

API_Export int stspi_get_plugin_info_v11(
    sts_plugin_info_v11_t* plugin_info)
{
    if (!plugin_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Fill in plugin information
    strncpy(plugin_info->plugin_name, "Vast Data OST Plugin", sizeof(plugin_info->plugin_name) - 1);
    strncpy(plugin_info->plugin_version, "1.0.0", sizeof(plugin_info->plugin_version) - 1);
    strncpy(plugin_info->vendor_name, "Vast Data", sizeof(plugin_info->vendor_name) - 1);
    strncpy(plugin_info->description, "NetBackup OpenStorage Plugin for Vast Data", sizeof(plugin_info->description) - 1);
    
    plugin_info->capabilities = STS_CAP_ASYNC_IO | STS_CAP_COPY_IMAGE | STS_CAP_EVENTS | STS_CAP_METADATA;
    plugin_info->supported_features = STS_FEATURE_DEDUP | STS_FEATURE_ENCRYPTION | STS_FEATURE_COMPRESSION;

    return STS_SUCCESS;
}

API_Export int stspi_health_check_v11(
    stsp_server_handle_t sh,
    sts_health_info_v11_t* health_info)
{
    std::cout << "Vast Data OST Plugin: Performing health check" << std::endl;

    if (!sh || !sh->vast_server || !health_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Initialize health info
        memset(health_info, 0, sizeof(sts_health_info_v11_t));

        // Check if server is connected
        if (!sh->vast_server->isConnected()) {
            health_info->status = STS_HEALTH_ERROR;
            strncpy(health_info->message, "Not connected to Vast Data cluster", sizeof(health_info->message) - 1);
            return STS_SUCCESS;
        }

        // Perform basic connectivity test by getting server info
        sts_server_info_v8_t server_info;
        int result = sh->vast_server->getServerInfo(&server_info);
        if (result != STS_SUCCESS) {
            health_info->status = STS_HEALTH_WARNING;
            strncpy(health_info->message, "Vast Data cluster connectivity issues", sizeof(health_info->message) - 1);
        } else {
            health_info->status = STS_HEALTH_OK;
            strncpy(health_info->message, "Vast Data cluster is healthy", sizeof(health_info->message) - 1);
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        health_info->status = STS_HEALTH_ERROR;
        strncpy(health_info->message, "Health check failed", sizeof(health_info->message) - 1);
        std::cerr << "Vast Data OST Plugin: Health check failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * Additional Utility and Configuration Functions
 *********************************************************************************/

API_Export int stspi_get_config_v11(
    stsp_server_handle_t sh,
    const char* config_key,
    char* config_value,
    size_t value_size)
{
    std::cout << "Vast Data OST Plugin: Getting configuration: " << config_key << std::endl;

    if (!sh || !sh->vast_server || !config_key || !config_value) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Handle common configuration keys
        if (strcmp(config_key, "server_name") == 0) {
            strncpy(config_value, sh->vast_server->getServerName().c_str(), value_size - 1);
        } else if (strcmp(config_key, "vms_endpoint") == 0) {
            strncpy(config_value, sh->vast_server->getVmsEndpoint().c_str(), value_size - 1);
        } else if (strcmp(config_key, "s3_endpoint") == 0) {
            strncpy(config_value, sh->vast_server->getS3Endpoint().c_str(), value_size - 1);
        } else if (strcmp(config_key, "connected") == 0) {
            strncpy(config_value, sh->vast_server->isConnected() ? "true" : "false", value_size - 1);
        } else {
            // Unknown configuration key
            strncpy(config_value, "", value_size - 1);
        }

        config_value[value_size - 1] = '\0';
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get config failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_set_config_v11(
    stsp_server_handle_t sh,
    const char* config_key,
    const char* config_value)
{
    std::cout << "Vast Data OST Plugin: Setting configuration: " << config_key
              << " = " << config_value << std::endl;

    if (!sh || !sh->vast_server || !config_key || !config_value) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Most configuration changes are not supported at runtime
        // Configuration should be done through VMS or configuration files
        std::cout << "Vast Data OST Plugin: Configuration changes not supported at runtime" << std::endl;
        return STS_ERR_NOT_SUPPORTED;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Set config failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_statistics_v11(
    stsp_server_handle_t sh,
    sts_statistics_v11_t* stats)
{
    std::cout << "Vast Data OST Plugin: Getting statistics" << std::endl;

    if (!sh || !sh->vast_server || !stats) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Initialize statistics structure
        memset(stats, 0, sizeof(sts_statistics_v11_t));

        // In a real implementation, these would be retrieved from VMS API
        // For now, provide reasonable default values
        stats->total_capacity = 10ULL * 1024 * 1024 * 1024 * 1024; // 10TB
        stats->used_capacity = 2ULL * 1024 * 1024 * 1024 * 1024;   // 2TB
        stats->free_capacity = stats->total_capacity - stats->used_capacity;
        stats->total_images = 0; // Would be counted from actual S3 objects
        stats->total_bytes_written = 0; // Would be tracked from operations
        stats->total_bytes_read = 0;    // Would be tracked from operations

        // Performance metrics (would be collected from actual operations)
        stats->avg_read_time = 50;  // milliseconds
        stats->avg_write_time = 100; // milliseconds
        stats->operations_per_second = 1000;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get statistics failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

/*********************************************************************************
 * Missing Core Image Functions - Complete Implementation  
 *********************************************************************************/

API_Export int stspi_create_image_v10(
    const stsp_lsu_t* lsu, 
    const sts_image_def_v10_t* imageDefinition, 
    int pendingFlag, 
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Creating image: " << imageDefinition->imageName << std::endl;
    
    if (!lsu || !imageDefinition || !imageHandle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate image handle
        *imageHandle = new stsp_image_handle_s;
        (*imageHandle)->image_ptr = nullptr; // Will be created by VastS3Client
        (*imageHandle)->lsu = *lsu;
        (*imageHandle)->image_def = *imageDefinition;
        (*imageHandle)->session = nullptr; // Will be set from server handle
        (*imageHandle)->need_copy_image = false;
        (*imageHandle)->is_target_null = false;
        (*imageHandle)->event_flag = 0;

        // Create VastImage object for the backup image
        VastImage* vast_image = new VastImage(imageDefinition->imageName, nullptr);
        if (!vast_image) {
            delete *imageHandle;
            *imageHandle = nullptr;
            return STS_ERR_INTERNAL;
        }

        // Initialize the image with the definition
        int result = vast_image->initialize(*imageDefinition, flags);
        if (result != STS_SUCCESS) {
            delete vast_image;
            delete *imageHandle;
            *imageHandle = nullptr;
            return result;
        }

        // Store the VastImage in the handle
        (*imageHandle)->image_ptr = vast_image;

        std::cout << "Vast Data OST Plugin: Image creation initialized successfully" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image creation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_open_image_v10(
    const stsp_lsu_t* lsu, 
    const sts_image_def_v10_t* imageDefinition, 
    int mode, 
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Opening image: " << imageDefinition->imageName 
              << " (mode: " << mode << ")" << std::endl;
    
    if (!lsu || !imageDefinition || !imageHandle) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Allocate image handle
        *imageHandle = new stsp_image_handle_s;
        (*imageHandle)->image_ptr = nullptr; // Will be opened by VastS3Client
        (*imageHandle)->lsu = *lsu;
        (*imageHandle)->image_def = *imageDefinition;
        (*imageHandle)->session = nullptr;
        (*imageHandle)->need_copy_image = false;
        (*imageHandle)->is_target_null = false;
        (*imageHandle)->event_flag = 0;

        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            delete *imageHandle;
            *imageHandle = nullptr;
            return STS_ERR_INVALID_PARAMETER;
        }

        // Create VastImage object for the backup image
        VastImage* vast_image = new VastImage(imageDefinition->imageName, vast_lsu);
        if (!vast_image) {
            delete *imageHandle;
            *imageHandle = nullptr;
            return STS_ERR_INTERNAL;
        }

        // Initialize the image for opening (read mode)
        int result = vast_image->initialize(*imageDefinition, mode);
        if (result != STS_SUCCESS) {
            delete vast_image;
            delete *imageHandle;
            *imageHandle = nullptr;
            return result;
        }

        // Store the VastImage in the handle
        (*imageHandle)->image_ptr = vast_image;

        std::cout << "Vast Data OST Plugin: Image opening completed successfully" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image opening failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_read_image(
    stsp_image_handle_t ih, 
    void* buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t* bytesRead)
{
    if (!ih || !buf || !bytesRead) {
        return STS_ERR_INVALID_PARAMETER;
    }

    std::cout << "Vast Data OST Plugin: Reading " << length << " bytes at offset " << offset << std::endl;

    try {
        *bytesRead = 0;
        
        if (!ih->image_ptr || !ih->session) {
            return STS_ERR_INVALID_HANDLE;
        }

        // Get server handle from session
        auto server_handle = static_cast<stsp_server_handle_t>(ih->session);
        if (!server_handle || !server_handle->vast_server) {
            return STS_ERR_INVALID_HANDLE;
        }

        // Generate S3 object key from LSU and image name
        std::string bucket_name = ih->lsu.lsuName;
        std::string object_key = std::string(ih->image_def.imageName) + ".backup";
        
        // Create S3 client instance for this operation
        VastS3Client s3_client;
        int result = s3_client.initialize(
            server_handle->vast_server->getS3Endpoint(),
            server_handle->vast_server->getAccessKey(),
            server_handle->vast_server->getSecretKey()
        );
        
        if (result != STS_SUCCESS) {
            std::cerr << "Failed to initialize S3 client for read operation" << std::endl;
            return result;
        }

        // Perform S3 GetObject with range
        result = s3_client.getObjectRange(bucket_name, object_key, 
                                         static_cast<char*>(buf), length, offset, bytesRead);
        
        if (result == STS_SUCCESS) {
            std::cout << "Successfully read " << *bytesRead << " bytes" << std::endl;
        } else {
            std::cerr << "S3 read operation failed with error: " << result << std::endl;
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Read operation failed: " << e.what() << std::endl;
        *bytesRead = 0;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_write_image(
    stsp_image_handle_t ih,  
    sts_stat_t* stat, 
    void* buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t* bytesWritten)
{
    if (!ih || !buf || !bytesWritten) {
        return STS_ERR_INVALID_PARAMETER;
    }

    std::cout << "Vast Data OST Plugin: Writing " << length << " bytes at offset " << offset << std::endl;

    try {
        *bytesWritten = 0;
        
        if (!ih->image_ptr || !ih->session) {
            return STS_ERR_INVALID_HANDLE;
        }

        // Get server handle from session
        auto server_handle = static_cast<stsp_server_handle_t>(ih->session);
        if (!server_handle || !server_handle->vast_server) {
            return STS_ERR_INVALID_HANDLE;
        }

        // Generate S3 object key from LSU and image name
        std::string bucket_name = ih->lsu.lsuName;
        std::string object_key = std::string(ih->image_def.imageName) + ".backup";
        
        // Create S3 client instance for this operation
        VastS3Client s3_client;
        int result = s3_client.initialize(
            server_handle->vast_server->getS3Endpoint(),
            server_handle->vast_server->getAccessKey(),
            server_handle->vast_server->getSecretKey()
        );
        
        if (result != STS_SUCCESS) {
            std::cerr << "Failed to initialize S3 client for write operation" << std::endl;
            return result;
        }

        // For large writes or non-zero offsets, use multipart upload
        if (length > 5 * 1024 * 1024 || offset > 0) { // 5MB threshold
            result = s3_client.putObjectMultipart(bucket_name, object_key,
                                                 static_cast<const char*>(buf), length, offset, bytesWritten);
        } else {
            // For small writes at offset 0, use simple put
            result = s3_client.putObject(bucket_name, object_key,
                                       static_cast<const char*>(buf), length, bytesWritten);
        }
        
        if (result == STS_SUCCESS) {
            std::cout << "Successfully wrote " << *bytesWritten << " bytes" << std::endl;
            
            // Update statistics if provided
            if (stat) {
                stat->bytes_written += *bytesWritten;
                stat->write_operations++;
            }
        } else {
            std::cerr << "S3 write operation failed with error: " << result << std::endl;
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Write operation failed: " << e.what() << std::endl;
        *bytesWritten = 0;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_delete_image_v10(
    const stsp_lsu_t* lsu, 
    const sts_image_def_v10_t* imageDefinition, 
    int asyncFlag)
{
    std::cout << "Vast Data OST Plugin: Deleting image: " << imageDefinition->imageName 
              << " (async: " << (asyncFlag ? "yes" : "no") << ")" << std::endl;
    
    if (!lsu || !imageDefinition) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get LSU from handle
        VastLSU* vast_lsu = static_cast<VastLSU*>(lsu->lsu_ptr);
        if (!vast_lsu) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Use LSU's deleteImage method
        int result = vast_lsu->deleteImage(imageDefinition, asyncFlag);
        if (result == STS_SUCCESS) {
            std::cout << "Vast Data OST Plugin: Image deleted successfully" << std::endl;
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image deletion failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_copy_image_v11(
    const stsp_lsu_t* to_lsu, 
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_t* from_lsu, 
    const sts_image_def_v10_t* from_img, 
    const sts_opname_v11_t imageset,
    int eventflag)
{
    std::cout << "Vast Data OST Plugin: Copying image from " << from_img->imageName 
              << " to " << to_img->imageName << std::endl;
    
    if (!to_lsu || !to_img || !from_lsu || !from_img) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Synchronous copy is an advanced feature for optimization
        // In a real implementation, this would use S3 copy operations:
        // 1. Generate source and destination S3 object keys
        // 2. Use S3 CopyObject for small images
        // 3. Use S3 multipart copy for large images
        // 4. Handle cross-bucket copies if LSUs are in different buckets

        std::cout << "Vast Data OST Plugin: Synchronous copy requested but not implemented" << std::endl;
        return STS_ERR_NOT_SUPPORTED;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image copy failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_close_image(stsp_image_handle_t ih, sts_stat_t* stat)
{
    if (!ih) {
        return STS_ERR_INVALID_PARAMETER;
    }

    std::cout << "Vast Data OST Plugin: Closing image " << ih->image_def.imageName << std::endl;

    try {
        // Update statistics if provided
        if (stat) {
            stat->close_operations++;
        }

        // Clean up image-specific resources
        if (ih->image_ptr) {
            delete static_cast<VastS3Client*>(ih->image_ptr);
            ih->image_ptr = nullptr;
        }

        // Clear the handle
        delete ih;
        
        std::cout << "Image closed successfully" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Close operation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_flush_image(stsp_image_handle_t ih, sts_stat_t* stat)
{
    if (!ih) {
        return STS_ERR_INVALID_PARAMETER;
    }

    std::cout << "Vast Data OST Plugin: Flushing image " << ih->image_def.imageName << std::endl;

    try {
        if (!ih->image_ptr || !ih->session) {
            return STS_ERR_INVALID_HANDLE;
        }

        // Get server handle from session
        auto server_handle = static_cast<stsp_server_handle_t>(ih->session);
        if (!server_handle || !server_handle->vast_server) {
            return STS_ERR_INVALID_HANDLE;
        }

        // For S3-based storage, flush typically means ensuring all multipart uploads are completed
        // and metadata is synchronized with the VMS
        std::string bucket_name = ih->lsu.lsuName;
        std::string object_key = std::string(ih->image_def.imageName) + ".backup";
        
        // Update image metadata via VMS REST API
        VastRestClient rest_client;
        int result = rest_client.initialize(
            server_handle->vast_server->getVmsEndpoint(),
            server_handle->vast_server->getAccessKey(),
            server_handle->vast_server->getSecretKey()
        );
        
        if (result == STS_SUCCESS) {
            // Sync image metadata and status
            result = rest_client.updateImageMetadata(bucket_name, object_key, ih->image_def);
            
            if (result == STS_SUCCESS) {
                std::cout << "Image flush completed successfully" << std::endl;
                
                // Update statistics if provided
                if (stat) {
                    stat->flush_operations++;
                }
            } else {
                std::cerr << "Failed to update image metadata during flush" << std::endl;
            }
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Flush operation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_get_image_info(stsp_image_handle_t ih, stsp_image_info_t* info)
{
    if (!ih || !info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        VastS3Client* s3Client = static_cast<VastS3Client*>(ih->image_ptr);
        if (!s3Client) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Get object metadata from S3
        std::map<std::string, std::string> metadata;
        if (!s3Client->getObjectMetadata(ih->image_def.imageName, metadata)) {
            return STS_ERR_NOT_FOUND;
        }

        // Fill in image information
        strncpy(info->imageName, ih->image_def.imageName, sizeof(info->imageName) - 1);
        info->imageName[sizeof(info->imageName) - 1] = '\0';
        
        // Get size from metadata or content length
        auto sizeIt = metadata.find("content-length");
        if (sizeIt != metadata.end()) {
            info->imageSize = std::stoull(sizeIt->second);
        } else {
            info->imageSize = ih->image_def.image_size;
        }
        
        info->blockSize = ih->image_def.block_size;
        info->isReadOnly = (ih->image_def.access_mode == STSP_ACCESS_READ_ONLY);
        
        // Set creation and modification times from metadata if available
        auto createdIt = metadata.find("x-amz-meta-created-time");
        if (createdIt != metadata.end()) {
            info->creationTime = std::stoull(createdIt->second);
        } else {
            info->creationTime = time(nullptr);
        }
        
        auto modifiedIt = metadata.find("last-modified");
        if (modifiedIt != metadata.end()) {
            // Parse HTTP date format if needed
            info->modificationTime = time(nullptr);
        } else {
            info->modificationTime = time(nullptr);
        }
        
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Get image info failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_resize_image(stsp_image_handle_t ih, uint64_t new_size)
{
    if (!ih) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        VastS3Client* s3Client = static_cast<VastS3Client*>(ih->image_ptr);
        if (!s3Client) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Check if new size is smaller than current size
        if (new_size < ih->image_def.image_size) {
            // Truncation - would require careful handling to avoid data loss
            // For now, we'll only support expansion
            return STS_ERR_NOT_SUPPORTED;
        }

        // For expansion, we just need to update the metadata
        // The actual object will be expanded as data is written
        std::map<std::string, std::string> metadata;
        metadata["x-amz-meta-original-size"] = std::to_string(ih->image_def.image_size);
        metadata["x-amz-meta-new-size"] = std::to_string(new_size);
        metadata["x-amz-meta-resize-time"] = std::to_string(time(nullptr));

        // Update the image definition
        ih->image_def.image_size = new_size;

        // Note: In a real implementation, you might want to update object metadata
        // This would require copying the object with new metadata in S3
        std::cout << "Vast Data OST Plugin: Image resized from " << metadata["x-amz-meta-original-size"] 
                  << " to " << new_size << " bytes" << std::endl;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Resize image failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_delete_image(const char* image_path)
{
    if (!image_path) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get configuration
        VastConfig config;
        if (!config.loadFromFile("vast_config.conf")) {
            std::cerr << "Vast Data OST Plugin: Failed to load configuration" << std::endl;
            return STS_ERR_CONFIG;
        }

        // Create S3 client for deletion
        VastS3Client s3Client(config.s3_endpoint, config.access_key, config.secret_key, config.bucket_name);

        // Extract object key from image path
        std::string object_key = image_path;
        if (object_key.find(config.bucket_name + "/") == 0) {
            object_key = object_key.substr(config.bucket_name.length() + 1);
        }

        // Delete the object from S3
        bool success = s3Client.deleteObject(object_key);
        if (!success) {
            std::cerr << "Vast Data OST Plugin: Failed to delete object: " << object_key << std::endl;
            return STS_ERR_IO;
        }

        std::cout << "Vast Data OST Plugin: Successfully deleted image: " << image_path << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Delete image failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_enumerate_images(stspi_image_info_t** image_list, int* count)
{
    if (!image_list || !count) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get configuration
        VastConfig config;
        if (!config.loadFromFile("vast_config.conf")) {
            std::cerr << "Vast Data OST Plugin: Failed to load configuration" << std::endl;
            return STS_ERR_CONFIG;
        }

        // Create S3 client for listing
        VastS3Client s3Client(config.s3_endpoint, config.access_key, config.secret_key, config.bucket_name);

        // List objects in the bucket
        std::vector<std::string> object_keys;
        bool success = s3Client.listObjects("", object_keys);
        if (!success) {
            std::cerr << "Vast Data OST Plugin: Failed to list objects" << std::endl;
            return STS_ERR_IO;
        }

        *count = static_cast<int>(object_keys.size());
        if (*count == 0) {
            *image_list = nullptr;
            return STS_SUCCESS;
        }

        // Allocate memory for image list
        *image_list = static_cast<stspi_image_info_t*>(malloc(*count * sizeof(stspi_image_info_t)));
        if (!*image_list) {
            return STS_ERR_OUT_OF_MEMORY;
        }

        // Fill image information
        for (int i = 0; i < *count; i++) {
            stspi_image_info_t* info = &(*image_list)[i];
            
            // Set image path
            std::string full_path = config.bucket_name + "/" + object_keys[i];
            strncpy(info->image_path, full_path.c_str(), sizeof(info->image_path) - 1);
            info->image_path[sizeof(info->image_path) - 1] = '\0';
            
            // Get object metadata for size
            std::map<std::string, std::string> metadata;
            size_t object_size = 0;
            if (s3Client.getObjectMetadata(object_keys[i], metadata, object_size)) {
                info->size = object_size;
            } else {
                info->size = 0;
            }
            
            // Set other fields
            info->block_size = config.block_size;
            info->is_read_only = false;
            info->creation_time = time(nullptr);
            info->modification_time = time(nullptr);
        }

        std::cout << "Vast Data OST Plugin: Enumerated " << *count << " images" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Enumerate images failed: " << e.what() << std::endl;
        if (*image_list) {
            free(*image_list);
            *image_list = nullptr;
        }
        *count = 0;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_copy_image(const char* src_image_path, const char* dst_image_path)
{
    if (!src_image_path || !dst_image_path) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get configuration
        VastConfig config;
        if (!config.loadFromFile("vast_config.conf")) {
            std::cerr << "Vast Data OST Plugin: Failed to load configuration" << std::endl;
            return STS_ERR_CONFIG;
        }

        // Parse source and destination paths
        std::string src_bucket, src_key, dst_bucket, dst_key;
        if (!parseImagePath(src_image_path, src_bucket, src_key) ||
            !parseImagePath(dst_image_path, dst_bucket, dst_key)) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Create S3 client
        VastS3Client s3Client(config.s3_endpoint, config.access_key, config.secret_key, src_bucket);

        // Check if source exists
        std::map<std::string, std::string> src_metadata;
        size_t src_size = 0;
        if (!s3Client.getObjectMetadata(src_key, src_metadata, src_size)) {
            std::cerr << "Vast Data OST Plugin: Source image not found: " << src_image_path << std::endl;
            return STS_ERR_NOT_FOUND;
        }

        // Use server-side copy if both buckets are the same
        if (src_bucket == dst_bucket) {
            bool success = s3Client.copyObject(src_key, dst_key);
            if (!success) {
                std::cerr << "Vast Data OST Plugin: Failed to copy image server-side" << std::endl;
                return STS_ERR_IO;
            }
        } else {
            // Cross-bucket copy - need to read and write
            // Read source data in chunks
            const size_t chunk_size = 1024 * 1024; // 1MB chunks
            std::vector<char> buffer(chunk_size);
            
            VastS3Client dst_s3Client(config.s3_endpoint, config.access_key, config.secret_key, dst_bucket);
            
            // Initialize multipart upload for destination
            std::string upload_id;
            if (!dst_s3Client.initiateMultipartUpload(dst_key, upload_id)) {
                std::cerr << "Vast Data OST Plugin: Failed to initiate multipart upload" << std::endl;
                return STS_ERR_IO;
            }

            std::vector<std::string> etags;
            size_t offset = 0;
            int part_number = 1;
            
            while (offset < src_size) {
                size_t read_size = std::min(chunk_size, src_size - offset);
                
                // Read from source
                if (!s3Client.readObject(src_key, offset, read_size, buffer.data())) {
                    dst_s3Client.abortMultipartUpload(dst_key, upload_id);
                    return STS_ERR_IO;
                }
                
                // Upload part to destination
                std::string etag;
                if (!dst_s3Client.uploadPart(dst_key, upload_id, part_number, buffer.data(), read_size, etag)) {
                    dst_s3Client.abortMultipartUpload(dst_key, upload_id);
                    return STS_ERR_IO;
                }
                
                etags.push_back(etag);
                offset += read_size;
                part_number++;
            }
            
            // Complete multipart upload
            if (!dst_s3Client.completeMultipartUpload(dst_key, upload_id, etags)) {
                dst_s3Client.abortMultipartUpload(dst_key, upload_id);
                return STS_ERR_IO;
            }
        }

        std::cout << "Vast Data OST Plugin: Successfully copied image from " << src_image_path 
                  << " to " << dst_image_path << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Copy image failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

API_Export int stspi_import_image(const char* image_path, const char* source_path, long long offset, long long size)
{
    if (!image_path || !source_path) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get configuration
        VastConfig config;
        if (!config.loadFromFile("vast_config.conf")) {
            std::cerr << "Vast Data OST Plugin: Failed to load configuration" << std::endl;
            return STS_ERR_CONFIG;
        }

        // Parse destination path
        std::string bucket, key;
        if (!parseImagePath(image_path, bucket, key)) {
            return STS_ERR_INVALID_PARAMETER;
        }

        // Open source file
        std::ifstream source_file(source_path, std::ios::binary);
        if (!source_file.is_open()) {
            std::cerr << "Vast Data OST Plugin: Cannot open source file: " << source_path << std::endl;
            return STS_ERR_NOT_FOUND;
        }

        // Get source file size if size is 0
        if (size == 0) {
            source_file.seekg(0, std::ios::end);
            size = source_file.tellg() - offset;
            source_file.seekg(0, std::ios::beg);
        }

        // Seek to offset
        source_file.seekg(offset);

        // Create S3 client
        VastS3Client s3Client(config.s3_endpoint, config.access_key, config.secret_key, bucket);

        // Upload data in chunks
        const size_t chunk_size = 1024 * 1024; // 1MB chunks
        std::vector<char> buffer(chunk_size);
        
        // For small files, use single upload
        if (size <= chunk_size) {
            buffer.resize(size);
            source_file.read(buffer.data(), size);
            
            if (!s3Client.putObject(key, buffer.data(), size)) {
                std::cerr << "Vast Data OST Plugin: Failed to upload data" << std::endl;
                return STS_ERR_IO;
            }
        } else {
            // Use multipart upload for large files
            std::string upload_id;
            if (!s3Client.initiateMultipartUpload(key, upload_id)) {
                std::cerr << "Vast Data OST Plugin: Failed to initiate multipart upload" << std::endl;
                return STS_ERR_IO;
            }

            std::vector<std::string> etags;
            long long remaining = size;
            int part_number = 1;
            
            while (remaining > 0) {
                size_t read_size = std::min(chunk_size, static_cast<size_t>(remaining));
                source_file.read(buffer.data(), read_size);
                
                if (source_file.gcount() != static_cast<std::streamsize>(read_size)) {
                    s3Client.abortMultipartUpload(key, upload_id);
                    return STS_ERR_IO;
                }
                
                std::string etag;
                if (!s3Client.uploadPart(key, upload_id, part_number, buffer.data(), read_size, etag)) {
                    s3Client.abortMultipartUpload(key, upload_id);
                    return STS_ERR_IO;
                }
                
                etags.push_back(etag);
                remaining -= read_size;
                part_number++;
            }
            
            // Complete multipart upload
            if (!s3Client.completeMultipartUpload(key, upload_id, etags)) {
                s3Client.abortMultipartUpload(key, upload_id);
                return STS_ERR_IO;
            }
        }

        std::cout << "Vast Data OST Plugin: Successfully imported " << size << " bytes from " 
                  << source_path << " to " << image_path << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Import image failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

extern "C" STS_RESULT StsImageExport(STS_HANDLE hSession, STS_HANDLE hImage, 
                                     const char* pszExportPath, STS_EXPORT_PARAMS* pParams)
{
    if (!hSession || !hImage || !pszExportPath) {
        return STS_ERR_INVALID_PARAM;
    }

    try {
        VastSession* session = reinterpret_cast<VastSession*>(hSession);
        VastImage* image = reinterpret_cast<VastImage*>(hImage);

        // Validate session
        if (!session->IsValid()) {
            return STS_ERR_INVALID_SESSION;
        }

        // Create export directory if needed
        std::string exportDir = std::string(pszExportPath);
        size_t lastSlash = exportDir.find_last_of("/\\");
        if (lastSlash != std::string::npos) {
            std::string dirPath = exportDir.substr(0, lastSlash);
            // Create directory structure if needed (simplified)
        }

        // Open source image for reading
        std::ifstream sourceFile(image->imagePath, std::ios::binary);
        if (!sourceFile.is_open()) {
            return STS_ERR_OPEN_FAILED;
        }

        // Create destination file
        std::ofstream destFile(pszExportPath, std::ios::binary);
        if (!destFile.is_open()) {
            sourceFile.close();
            return STS_ERR_CREATE_FAILED;
        }

        // Copy data in chunks
        const size_t bufferSize = 1024 * 1024; // 1MB chunks
        std::vector<char> buffer(bufferSize);
        
        while (sourceFile.good() && destFile.good()) {
            sourceFile.read(buffer.data(), bufferSize);
            std::streamsize bytesRead = sourceFile.gcount();
            
            if (bytesRead > 0) {
                destFile.write(buffer.data(), bytesRead);
                if (!destFile.good()) {
                    sourceFile.close();
                    destFile.close();
                    return STS_ERR_WRITE_FAILED;
                }
            }
        }

        sourceFile.close();
        destFile.close();

        // Verify export completed successfully
        if (!sourceFile.eof()) {
            return STS_ERR_READ_FAILED;
        }

        return STS_OK;
    }
    catch (const std::exception& e) {
        return STS_ERR_INTERNAL_ERROR;
    }
}