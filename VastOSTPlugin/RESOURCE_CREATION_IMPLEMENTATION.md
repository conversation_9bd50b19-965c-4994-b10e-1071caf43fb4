# Vast Data OST Plugin - Resource Creation Implementation

## Overview

The Vast Data OST plugin now includes **complete resource creation** functionality to automatically set up the required infrastructure on first use. This addresses the critical gap where tenants, views, and buckets need to be created before NetBackup can use them.

## Resource Creation Strategy

### 1. **When Resources Are Created**

#### **Tenant Creation (First-time setup)**
- **Trigger**: `stspi_open_lsu_list_v11()` when no tenants exist
- **Function**: `VastStorageServer::ensureTenant()`
- **API Call**: `POST /api/tenants/`
- **Default**: Create "netbackup" tenant with S3 enabled
- **Frequency**: Once per cluster

#### **View Creation (First-time setup)**
- **Trigger**: `stspi_open_lsu_list_v11()` when no S3 views exist in tenant
- **Function**: `VastStorageServer::ensureView()`
- **API Call**: `POST /api/views/`
- **Default**: Create "netbackup-s3" view with S3 enabled
- **Frequency**: Once per tenant

#### **Bucket Creation (On-demand)**
- **Trigger**: `stspi_open_lsu_list_v11()` when no buckets exist
- **Function**: `VastStorageServer::ensureDefaultBuckets()`
- **API Call**: `PUT /{bucket}` (via S3 API)
- **Default**: Create "netbackup-pool-01" bucket
- **Frequency**: Once per cluster (creates default bucket)

### 2. **Resource Creation Flow**

```
NetBackup First Use          OST Interface                 Vast Data API Calls
─────────────────────────────────────────────────────────────────────────────
1. Initial Discovery   →     stspi_open_lsu_list_v11()  →  GET /api/tenants/
                                                        →  POST /api/tenants/ (if none)
                                                        →  GET /api/views/
                                                        →  POST /api/views/ (if none S3)
                                                        →  GET / (list buckets)
                                                        →  PUT /netbackup-pool-01 (if none)

2. LSU Enumeration     →     stspi_list_lsu()           →  (enumerate created buckets)

3. First Backup        →     stspi_create_image_v10()   →  HEAD /netbackup-pool-01
                                                        →  POST /netbackup-pool-01/netbackup/{name}/{date}/data?uploads
```

## Implementation Details

### 1. **Infrastructure Orchestration**

```cpp
int VastStorageServer::ensureInfrastructure() {
    // Step 1: Ensure default tenant exists
    int result = ensureTenant("netbackup");
    if (result != STS_SUCCESS) return result;
    
    // Step 2: Ensure default S3 view exists
    result = ensureView("netbackup-s3", "netbackup");
    if (result != STS_SUCCESS) return result;
    
    // Step 3: Ensure default buckets exist
    result = ensureDefaultBuckets();
    if (result != STS_SUCCESS) return result;
    
    return STS_SUCCESS;
}
```

### 2. **Tenant Creation**

```cpp
int VastStorageServer::ensureTenant(const std::string& tenant_name) {
    // Check if tenant already exists
    std::vector<VastTenantInfo> tenants;
    int result = m_rest_client->listTenants(tenants);
    
    for (const auto& tenant : tenants) {
        if (tenant.name == tenant_name) {
            return STS_SUCCESS; // Already exists
        }
    }
    
    // Create tenant if it doesn't exist
    VastTenantInfo tenant_info;
    tenant_info.name = tenant_name;
    tenant_info.ssd_enabled = true;
    tenant_info.trash_enabled = true;
    
    return m_rest_client->createTenant(tenant_info);
}
```

### 3. **View Creation**

```cpp
int VastStorageServer::ensureView(const std::string& view_name, const std::string& tenant_name) {
    // Check if view already exists
    std::vector<VastViewInfo> views;
    int result = m_rest_client->listViews(views);
    
    for (const auto& view : views) {
        if (view.name == view_name && view.tenant_name == tenant_name) {
            return STS_SUCCESS; // Already exists
        }
    }
    
    // Create S3-enabled view
    VastViewInfo view_info;
    view_info.name = view_name;
    view_info.path = "/" + view_name;
    view_info.tenant_name = tenant_name;
    view_info.nfs_enabled = false;
    view_info.smb_enabled = false;
    view_info.s3_enabled = true;  // Enable S3 for NetBackup
    
    return m_rest_client->createView(view_info);
}
```

### 4. **Bucket Creation**

```cpp
int VastStorageServer::ensureDefaultBuckets() {
    // List existing buckets
    std::vector<std::string> existing_buckets;
    int result = m_s3_client->listBuckets(existing_buckets);
    
    // If no buckets exist, create a default one
    if (result == STS_SUCCESS && existing_buckets.empty()) {
        std::string default_bucket = "netbackup-pool-01";
        result = m_s3_client->createBucket(default_bucket);
    }
    
    return result;
}
```

## Default Resource Configuration

### **Tenant: "netbackup"**
- **Purpose**: Isolate NetBackup data from other applications
- **Configuration**: 
  - S3 enabled: `true`
  - SSD enabled: `true`
  - Trash enabled: `true`
  - Default VIP pool: (cluster default)

### **View: "netbackup-s3"**
- **Purpose**: S3-enabled volume for bucket operations
- **Configuration**:
  - Tenant: `netbackup`
  - Path: `/netbackup-s3`
  - NFS enabled: `false`
  - SMB enabled: `false`
  - S3 enabled: `true`

### **Bucket: "netbackup-pool-01"**
- **Purpose**: Default storage container for backup images
- **Configuration**:
  - Created in "netbackup-s3" view context
  - Standard S3 bucket with no special configuration

## Benefits

### **1. Zero-Configuration Setup**
- NetBackup administrators don't need to pre-create Vast Data resources
- Plugin automatically sets up required infrastructure on first use
- Reduces deployment complexity and potential configuration errors

### **2. Idempotent Operations**
- All creation functions check for existing resources first
- Safe to run multiple times without creating duplicates
- Handles partial setup scenarios gracefully

### **3. Production-Ready Defaults**
- Sensible default names and configurations
- S3-optimized settings for backup workloads
- Proper tenant isolation for multi-application environments

### **4. Error Handling**
- Comprehensive error checking and logging
- Graceful fallback when resources already exist
- Clear error messages for troubleshooting

## Integration Points

### **OST Interface Integration**
- `stspi_open_lsu_list_v11()` now calls `ensureInfrastructure()`
- Transparent to NetBackup - no API changes required
- Works with existing NetBackup configuration workflows

### **Vast Data API Integration**
- Uses existing VMS REST API for tenant/view creation
- Uses existing S3 API for bucket creation
- Leverages existing authentication and error handling

## Testing Scenarios

### **Fresh Cluster (No Resources)**
1. Plugin creates "netbackup" tenant
2. Plugin creates "netbackup-s3" view in tenant
3. Plugin creates "netbackup-pool-01" bucket
4. NetBackup discovers 1 LSU: "netbackup-pool-01"

### **Existing Tenant (No Views)**
1. Plugin finds existing "netbackup" tenant
2. Plugin creates "netbackup-s3" view
3. Plugin creates "netbackup-pool-01" bucket
4. NetBackup discovers 1 LSU: "netbackup-pool-01"

### **Existing Infrastructure**
1. Plugin finds existing tenant, view, and buckets
2. No creation operations performed
3. NetBackup discovers existing LSUs

This implementation ensures that the Vast Data OST plugin can be deployed in any environment and automatically create the necessary infrastructure for NetBackup operations.
